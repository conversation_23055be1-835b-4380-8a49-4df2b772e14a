"""
<PERSON><PERSON><PERSON> to find and log all AutomationIds on screen
"""
import time
from pywinauto import Desktop
from pywinauto.findwindows import find_elements

def find_all_automation_ids():
    """Find and log all AutomationIds on the current screen"""
    print("=== Finding All AutomationIds on Screen ===")
    print("Scanning all windows and controls...")
    
    try:
        desktop = Desktop(backend="uia")
        
        # Get all top-level windows
        windows = desktop.windows()
        print(f"Found {len(windows)} top-level windows")
        
        all_automation_ids = set()
        
        for i, window in enumerate(windows):
            try:
                window_title = window.window_text()
                window_auto_id = getattr(window, 'auto_id', lambda: 'N/A')()
                
                print(f"\n--- Window {i+1}: '{window_title}' ---")
                print(f"AutomationId: {window_auto_id}")
                
                if window_auto_id and window_auto_id != 'N/A':
                    all_automation_ids.add(window_auto_id)
                
                # Get all descendants (controls) of this window
                try:
                    descendants = window.descendants()
                    print(f"Found {len(descendants)} controls in this window")
                    
                    for j, control in enumerate(descendants):
                        try:
                            control_auto_id = getattr(control, 'auto_id', lambda: 'N/A')()
                            control_class = control.class_name()
                            control_text = control.window_text()[:50]  # First 50 chars
                            
                            if control_auto_id and control_auto_id != 'N/A':
                                all_automation_ids.add(control_auto_id)
                                print(f"  Control {j+1}: {control_class} - '{control_text}' (ID: {control_auto_id})")
                            
                        except Exception as e:
                            print(f"  Control {j+1}: <error getting info: {e}>")
                            
                except Exception as e:
                    print(f"  Error getting descendants: {e}")
                    
            except Exception as e:
                print(f"Window {i+1}: <error: {e}>")
        
        # Summary of all unique AutomationIds
        print(f"\n=== SUMMARY: Found {len(all_automation_ids)} unique AutomationIds ===")
        for auto_id in sorted(all_automation_ids):
            print(f"  {auto_id}")
        
        # Save to file
        with open("automation_ids_found.txt", "w", encoding="utf-8") as f:
            f.write("=== All AutomationIds Found ===\n")
            f.write(f"Total: {len(all_automation_ids)}\n\n")
            for auto_id in sorted(all_automation_ids):
                f.write(f"{auto_id}\n")
        
        print(f"\n✅ Results saved to 'automation_ids_found.txt'")
        
        return list(all_automation_ids)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def find_hps_automation_ids():
    """Find AutomationIds specifically in HPS windows"""
    print("\n=== Finding HPS-specific AutomationIds ===")
    
    try:
        desktop = Desktop(backend="uia")
        
        # Look for HPS windows specifically
        hps_keywords = ["HPS", "hps", "Hệ thống", "quản lý", "bệnh viện", "frmMain", "frmTI_dangnhap"]
        hps_automation_ids = set()
        
        for keyword in hps_keywords:
            try:
                window = desktop.window(title_re=keyword)
                if window.exists():
                    print(f"Found HPS window: '{window.window_text()}'")
                    
                    # Get all controls in this window
                    descendants = window.descendants()
                    print(f"  Found {len(descendants)} controls")
                    
                    for control in descendants:
                        try:
                            control_auto_id = getattr(control, 'auto_id', lambda: 'N/A')()
                            control_class = control.class_name()
                            control_text = control.window_text()[:30]
                            
                            if control_auto_id and control_auto_id != 'N/A':
                                hps_automation_ids.add(control_auto_id)
                                print(f"    {control_class}: '{control_text}' -> {control_auto_id}")
                                
                        except:
                            pass
                            
            except:
                pass
        
        # Also try by automation ID
        hps_auto_ids = ["frmMain", "frmTI_dangnhap"]
        for auto_id in hps_auto_ids:
            try:
                window = desktop.window(auto_id=auto_id)
                if window.exists():
                    print(f"Found HPS window by ID: {auto_id}")
                    
                    descendants = window.descendants()
                    for control in descendants:
                        try:
                            control_auto_id = getattr(control, 'auto_id', lambda: 'N/A')()
                            control_class = control.class_name()
                            control_text = control.window_text()[:30]
                            
                            if control_auto_id and control_auto_id != 'N/A':
                                hps_automation_ids.add(control_auto_id)
                                print(f"    {control_class}: '{control_text}' -> {control_auto_id}")
                                
                        except:
                            pass
                            
            except:
                pass
        
        print(f"\n=== HPS AutomationIds Summary ===")
        print(f"Found {len(hps_automation_ids)} HPS-specific AutomationIds:")
        for auto_id in sorted(hps_automation_ids):
            print(f"  {auto_id}")
        
        # Save HPS-specific IDs
        with open("hps_automation_ids.txt", "w", encoding="utf-8") as f:
            f.write("=== HPS AutomationIds Found ===\n")
            f.write(f"Total: {len(hps_automation_ids)}\n\n")
            for auto_id in sorted(hps_automation_ids):
                f.write(f"{auto_id}\n")
        
        print(f"✅ HPS AutomationIds saved to 'hps_automation_ids.txt'")
        
        return list(hps_automation_ids)
        
    except Exception as e:
        print(f"❌ Error finding HPS AutomationIds: {e}")
        return []

if __name__ == "__main__":
    print("Make sure HPS is open, then press Enter to start scanning...")
    input()
    
    # Find all AutomationIds
    all_ids = find_all_automation_ids()
    
    # Find HPS-specific AutomationIds
    hps_ids = find_hps_automation_ids()
    
    print(f"\n=== Final Summary ===")
    print(f"Total AutomationIds found: {len(all_ids)}")
    print(f"HPS-specific AutomationIds: {len(hps_ids)}")
    print("Check the generated files for detailed results.")
