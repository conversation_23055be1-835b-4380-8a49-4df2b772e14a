# 📝 Workflow Checklist (Automation HPS.exe)

## 1. Khởi động & Đăng nhập
- [ ] Mở `HPS.exe`
- [ ] Đăng nhập tài khoản

## 2. Đọc Excel Input
- [ ] Mở file Excel chỉ định (dùng `openpyxl`)
- [ ] Lặp qua các dòng dữ liệu, lấy:
  - `MA_BN`
  - `MA_DOI_TUONG`
  - `NGAY_VAO`
  - `NGAY_RA`

## 3. T<PERSON>o & Sửa Hồ Sơ Bệnh Án

### 3.1 Mở menu
- [ ] Chọn menu **8. Tiện ích → Bệnh nhân → <PERSON><PERSON> sơ bệnh án - EMR**

### 3.2 Nhập bệnh nhân mới
- [ ] Nhấn nút **Mới** (`BTN_MOI`)
- [ ] Nhập **Mã BN** (`TXT_MABN`)
- [ ] Nhấn **Enter**

### 3.3 So sánh & chọn đợt điều trị
- [ ] L<PERSON>y danh sách **Đợt điều trị Row <n>**
- [ ] So sánh giá trị **NGAYVAO** trong Excel với `ValuePattern.Value`
- [ ] Nếu trùng, chọn Row đó (click 1 lần)

### 3.4 Sửa đối tượng
- [ ] Chuyển sang tab **Sửa đối tượng** (`TAB_SUA_DOITUONG`)
- [ ] Kiểm tra combobox **ĐKKB chính** (`CBO_DKKBCHINH`)
  - Nếu value = `"Bệnh viện quân y B"`:
    - Nếu `MA_DOI_TUONG = 2`:
      - Chọn lại `"2"` trong `CBO_DOITUONG`
      - Click label **Mã đối tượng** (`LBL_DOITUONG`) để lưu
      - Nếu có popup thông báo → nhấn **OK** (`BTN_OK`)
  - Nếu value khác `"Bệnh viện quân y B"`:
    - Kiểm tra combobox **Nơi giới thiệu** (`CBO_NOIGIOITHIEU`)
    - Nếu có value → chọn `"1.3"` trong `CBO_DOITUONG`
    - Nếu không có value → chọn `"1.15"` trong `CBO_DOITUONG`
    - Click label **Mã đối tượng** (`LBL_DOITUONG`) để lưu
    - Nếu có popup thông báo → nhấn **OK** (`BTN_OK`)

## 4. Logging & Xử lý lỗi
- [ ] Mọi thao tác được ghi log (`logging`)
- [ ] Nếu control không tìm thấy → log warning, bỏ qua và sang record kế tiếp

