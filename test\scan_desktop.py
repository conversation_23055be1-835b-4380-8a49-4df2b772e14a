import uiautomation as auto

def scan(max_ids=3):
    count = 0
    # <PERSON><PERSON><PERSON> tất cả cửa sổ top-level
    for window in auto.GetRootControl().GetChildren():
        if window.AutomationId:
            print(f"Window: Name={window.Name} | Class={window.ClassName} | AutomationId={window.AutomationId}")
            count += 1
            if count >= max_ids:
                return
        for child in window.GetChildren():
            if child.AutomationId:
                print(f"  Child: Name={child.Name} | Class={child.ClassName} | AutomationId={child.AutomationId}")
                count += 1
                if count >= max_ids:
                    return

if __name__ == "__main__":
    scan()
