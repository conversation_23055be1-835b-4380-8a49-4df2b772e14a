import tkinter as tk
import psutil
from pywinauto import Application

HPS_EXE = "HS.exe"

def get_hps_pid():
    """Tìm PID hiện tại của HPS theo tên exe"""
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] and proc.info['name'].lower() == HPS_EXE.lower():
            return proc.info['pid']
    return None

def scan_control(control):
    """Đệ quy scan control con và in AutomationId"""
    try:
        aid = control.element_info.automation_id
        name = control.window_text()
        cls = control.element_info.class_name
        if aid or name:
            print(f"AutomationId: {aid} | Name={name} | Class={cls}")
    except:
        pass

    try:
        for child in control.children():
            scan_control(child)
    except:
        pass

def scan_hps():
    pid = get_hps_pid()
    if not pid:
        print(f"Không tìm thấy process {HPS_EXE}!")
        return

    print(f"<PERSON>ắt đầu quét HPS (PID={pid})...")
    try:
        app = Application(backend="uia").connect(process=pid)
        windows = app.windows()
        if not windows:
            print("Không tìm thấy cửa sổ nào trong process HPS!")
            return

        for w in windows:
            print(f"\n--- Top-level Window Class={w.element_info.class_name} ---")
            scan_control(w)

    except Exception as e:
        print(f"Lỗi quét: {e}")

# GUI Tkinter
root = tk.Tk()
root.title("Scan AutomationId HPS Dynamic")
root.geometry("400x120")

btn_scan = tk.Button(root, text="Quét toàn bộ AutomationId HPS", command=scan_hps)
btn_scan.pack(expand=True, fill='both', padx=20, pady=20)

root.mainloop()
