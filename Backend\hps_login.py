"""
HPS Login Automation Module
Handles automatic login to HPS application using pywinauto
"""
import time
import logging
import os
import psutil
from pywinauto import Application, Desktop
from pywinauto.findwindows import ElementNotFoundError
from pywinauto.timings import TimeoutError as PywinautoTimeoutError
from config import Config


class HPSLogin:
    """Handles HPS application login automation"""
    
    def __init__(self, config: Config):
        self.config = config
        self.app = None
        self.main_window = None
        self.login_window = None
        self.logger = logging.getLogger(__name__)
        self.hps_pid = None
    
    

    def connect_to_hps_process(self):
        """Kết nối với HPS process đang chạy - sử dụng phương thức tìm cửa sổ phù hợp nhất"""
        try:
            from pywinauto import findwindows
            
            self.logger.info("🔍 Đang tìm ứng dụng HPS...")
            
            # Method 1: Tì<PERSON> tất cả cửa sổ có chứa "Đăng nhập" hoặc "HPS" trong title
            windows = findwindows.find_windows(title_re=".*HPS.*")
            
            if not windows:
                self.logger.warning("Không tìm thấy cửa sổ ứng dụng HPS nào")
                # Fallback: try to find by process
                return self._connect_by_process()
            
            # Tìm cửa sổ phù hợp nhất bằng AutomationId
            target_window = None
            for window_handle in windows:
                try:
                    # Thử kết nối với cửa sổ
                    app_temp = Application(backend="uia").connect(handle=window_handle)
                    window_temp = app_temp.window(handle=window_handle)
                    
                    # Kiểm tra AutomationId có phù hợp không
                    try:
                        auto_id = window_temp.element_info.automation_id
                        if auto_id == "frmTI_dangnhap":
                            target_window = window_handle
                            self.app = app_temp
                            self.main_window = window_temp
                            self.login_window = window_temp
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ đăng nhập: {auto_id}")
                            return True
                        elif auto_id == "frmMain":
                            target_window = window_handle
                            self.app = app_temp
                            self.main_window = window_temp
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ chính: {auto_id}")
                            return True
                    except:
                        # Nếu không có AutomationId, kiểm tra title
                        window_title = window_temp.window_text()
                        if "Đăng nhập" in window_title:
                            target_window = window_handle
                            self.app = app_temp
                            self.main_window = window_temp
                            self.login_window = window_temp
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ đăng nhập: {window_title}")
                            return True
                        elif "HPS" in window_title:
                            target_window = window_handle
                            self.app = app_temp
                            self.main_window = window_temp
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ HPS: {window_title}")
                            return True
                        
                except Exception as e:
                    self.logger.debug(f"Không thể kết nối với window {window_handle}: {e}")
                    continue
            
            # Nếu không tìm thấy cửa sổ phù hợp, thử kết nối theo process
            if not target_window:
                return self._connect_by_process()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to HPS process: {e}")
            return False
    
    
    
    
    
    def _save_automation_ids(self, automation_ids):
        """Save AutomationIds to file for reference"""
        try:
            filename = "hps_automation_ids_auto_scan.txt"
            with open(filename, "w", encoding="utf-8") as f:
                f.write("=== HPS AutomationIds Auto Scan ===\n")
                f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total: {len(automation_ids)}\n\n")
                for auto_id in sorted(automation_ids):
                    f.write(f"{auto_id}\n")
            self.logger.info(f"AutomationIds saved to {filename}")
        except Exception as e:
            self.logger.warning(f"Could not save AutomationIds: {e}")
    
    def start_hps(self) -> bool:
        """
        Tìm và kết nối với ứng dụng HPS
        Returns: True nếu thành công, False nếu thất bại
        """
        try:
            from pywinauto import findwindows
            
            self.logger.info("🔍 Đang tìm ứng dụng HPS...")
            
            # Tìm tất cả cửa sổ có chứa "HPS" trong title
            windows = findwindows.find_windows(title_re=".*HPS.*")
            
            if not windows:
                self.logger.error("❌ Không tìm thấy cửa sổ ứng dụng HPS nào")
                # Try to start new HPS application
                return self._start_new_hps()
            
            # Tìm cửa sổ phù hợp nhất
            target_window = None
            self.logger.info(f"Found {len(windows)} HPS windows, checking titles...")
            
            for i, window_handle in enumerate(windows):
                try:
                    # Thử kết nối với cửa sổ
                    app_temp = Application().connect(handle=window_handle)
                    window_temp = app_temp.window(handle=window_handle)
                    
                    # Kiểm tra title có chứa pattern mong muốn
                    window_title = window_temp.window_text()
                    self.logger.info(f"  Window {i}: '{window_title}'")
                    
                    if "HPS" in window_title and "Trần Nguyên Tiến" in window_title:
                        target_window = window_handle
                        self.app = app_temp
                        self.main_window = window_temp
                        self.logger.info(f"✅ Đã tìm thấy và kết nối với: {window_title}")
                        break
                    elif "HPS" in window_title and not "đăng nhập" in window_title.lower():
                        # Found HPS window but not with specific name - might be main window
                        self.logger.info(f"Found HPS window (not with specific name): '{window_title}'")
                        if not target_window:  # Use first HPS window as fallback
                            target_window = window_handle
                            self.app = app_temp
                            self.main_window = window_temp
                        
                except Exception as e:
                    self.logger.debug(f"Không thể kết nối với window {window_handle}: {e}")
                    continue
            
            if not target_window:
                # Thử cách khác: kết nối theo process
                self.logger.info("🔄 Thử tìm theo danh sách process...")
                try:
                    # Tìm process có tên chứa "HPS" hoặc các tên thường gặp
                    possible_names = ["HPS", "hps", "Hospital", "BHXH", "BHYT"]
                    
                    for process_name in possible_names:
                        try:
                            self.app = Application().connect(path=f"{process_name}.exe")
                            self.main_window = self.app.window(auto_id="frmMain")
                            self.logger.info(f"✅ Đã kết nối với process: {process_name}.exe")
                            break
                        except:
                            continue
                            
                except Exception as e:
                    self.logger.debug(f"Không thể kết nối theo process: {e}")
            
            if self.app and self.main_window:
                # Đảm bảo cửa sổ hiển thị - KHÔNG set_focus() toàn cửa sổ để tránh chiếm chuột
                try:
                    # Chỉ restore nếu bị minimize, không set_focus() toàn cửa sổ
                    if self.main_window.is_minimized():
                        self.main_window.restore()
                        self.logger.info("✅ Đã restore cửa sổ HPS")
                    
                    # Kiểm tra kết nối thành công
                    title = self.main_window.window_text()
                    self.logger.info(f"✅ Đã kết nối với ứng dụng HPS: {title[:50]}...")
                    self.logger.info("🚀 Sẽ chỉ click vào controls cần thiết - không focus toàn cửa sổ!")
                    return True
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ Không thể kiểm tra cửa sổ: {e}")
                    return True  # Vẫn tiếp tục nếu kết nối được
            else:
                self.logger.error("❌ Không thể kết nối với ứng dụng HPS")
                # Try to start new HPS application
                return self._start_new_hps()
                
        except Exception as e:
            self.logger.error(f"❌ Lỗi khi tìm ứng dụng: {e}")
            # Try to start new HPS application
            return self._start_new_hps()
    
    def _start_new_hps(self) -> bool:
        """
        Start new HPS application if not found running
        """
        try:
            hps_path = self.config.get_hps_path()
            if not hps_path or not os.path.exists(hps_path):
                self.logger.error(f"HPS executable not found at: {hps_path}")
                return False
            
            self.logger.info("🚀 Starting new HPS application...")
            working_dir = os.path.dirname(hps_path)
            
            try:
                self.app = Application(backend="uia").start(cmd_line=hps_path, work_dir=working_dir)
                self.logger.info("✅ HPS application started with UIA backend")
            except Exception as e:
                self.logger.warning(f"UIA backend failed: {e}, trying win32...")
                try:
                    self.app = Application(backend="win32").start(cmd_line=hps_path, work_dir=working_dir)
                    self.logger.info("✅ HPS application started with win32 backend")
                except Exception as e2:
                    self.logger.error(f"Both backends failed: {e2}")
                    return False
            
            # Wait for application to load
            self.logger.info("⏳ Waiting for HPS to load...")
            time.sleep(2)
            
            # Handle startup dialogs
            self._handle_startup_dialogs()
            
            # Try to find main/login window
            main_window = self._find_main_window()
            
            if not main_window:
                self.logger.error("❌ Could not find HPS main window")
                return False

            self.main_window = main_window
            
            # Focus on the HPS window
            self._focus_hps_window()
            
            self.logger.info("✅ HPS application started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start new HPS: {e}")
            return False

    def _find_main_window_from_process(self):
        """Find main window from connected HPS process"""
        try:
            if not self.app:
                return None
            
            windows = self.app.windows()
            if not windows:
                return None
            
            # Look for specific window types
            for window in windows:
                try:
                    class_name = window.element_info.class_name
                    auto_id = window.element_info.automation_id
                    
                    # Check for login window first
                    if auto_id == "frmTI_dangnhap":
                        self.logger.info("Found login window (frmTI_dangnhap)")
                        self.login_window = window
                        return window
                    
                    # Check for main window
                    if auto_id == "frmMain":
                        self.logger.info("Found main window (frmMain)")
                        return window
                    
                    # Check by class name
                    if "Form" in class_name or "Window" in class_name:
                        self.logger.info(f"Found potential main window: {class_name}")
                        return window
                        
                except Exception as e:
                    self.logger.warning(f"Error checking window: {e}")
                    continue
            
            # If no specific window found, return first window
            if windows:
                self.logger.info("Using first available window")
                return windows[0]
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding main window from process: {e}")
            return None

    
    def _handle_startup_dialogs(self):
        """Handle error dialogs that might appear during startup - optimized for speed"""
        try:
            # Wait briefly for dialogs to appear
            time.sleep(1)  # Giảm từ 2s xuống 1s
            
            # Look for common error dialog patterns
            error_titles = ["Error", "Lỗi", "Warning", "Cảnh báo", "OK", "Thông báo"]
            
            for title in error_titles:
                try:
                    # Try UIA backend first (faster)
                    dialog = self.app.window(title_re=title, backend="uia")
                    if dialog.exists(timeout=0.5):  # Giảm timeout
                        self.logger.warning(f"Found dialog: {title}")
                        self._dismiss_dialog(dialog)
                        break
                except:
                    continue
                    
        except Exception as e:
            self.logger.warning(f"Error handling startup dialogs: {e}")
    
    def _dismiss_dialog(self, dialog):
        """Dismiss a dialog by clicking OK or pressing Enter"""
        try:
            # Try to find OK button
            ok_button = dialog.child_window(class_name="Button")
            if ok_button.exists():
                ok_button.click()
                self.logger.info("Clicked OK button on dialog")
                time.sleep(1)
            else:
                # Try to press Enter
                dialog.type_keys("{ENTER}")
                self.logger.info("Pressed Enter on dialog")
                time.sleep(1)
        except Exception as e:
            self.logger.warning(f"Could not dismiss dialog: {e}")
    
    def _find_main_window(self):
        """Find the main HPS window using automationId - sử dụng phương thức tìm cửa sổ phù hợp nhất"""
        try:
            from pywinauto import findwindows
            
            self.logger.info("🔍 Tìm cửa sổ HPS phù hợp nhất...")
            
            # Method 1: Tìm tất cả cửa sổ có chứa "Đăng nhập" trong title (ngày động)
            windows = findwindows.find_windows(title_re=".*Đăng nhập.*")
            
            if not windows:
                self.logger.warning("Không tìm thấy cửa sổ ứng dụng HPS nào")
                return self._find_window_fallback()
            
            # Tìm cửa sổ phù hợp nhất bằng AutomationId
            for window_handle in windows:
                try:
                    # Thử kết nối với cửa sổ
                    app_temp = Application(backend="uia").connect(handle=window_handle)
                    window_temp = app_temp.window(handle=window_handle)
                    
                    # Kiểm tra AutomationId có phù hợp không
                    try:
                        auto_id = window_temp.element_info.automation_id
                        if auto_id == "frmTI_dangnhap":
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ đăng nhập: {auto_id}")
                            self.login_window = window_temp
                            return window_temp
                        elif auto_id == "frmMain":
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ chính: {auto_id}")
                            return window_temp
                    except:
                        # Nếu không có AutomationId, kiểm tra title
                        window_title = window_temp.window_text()
                        if "HPS" in window_title and ("đăng nhập" in window_title.lower() or "login" in window_title.lower()):
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ đăng nhập: {window_title}")
                            self.login_window = window_temp
                            return window_temp
                        elif "HPS" in window_title:
                            self.logger.info(f"✅ Đã tìm thấy cửa sổ HPS: {window_title}")
                            return window_temp
                        
                except Exception as e:
                    self.logger.debug(f"Không thể kết nối với window {window_handle}: {e}")
                    continue
            
            # Fallback methods
            return self._find_window_fallback()
                
        except Exception as e:
            self.logger.warning(f"Error finding main window: {e}")
            return self._find_window_fallback()
    
    def _find_window_fallback(self):
        """Fallback methods to find HPS window"""
        try:
            # Method 1: Use Desktop to find windows by automationId
            try:
                desktop = Desktop(backend="uia")
                
                # Try login window first
                try:
                    login_window = desktop.window(auto_id="frmTI_dangnhap")
                    if login_window.exists(timeout=1):
                        self.logger.info("Found login window via Desktop (frmTI_dangnhap)")
                        self.login_window = login_window
                        return login_window
                except:
                    pass
                
                # Try main window
                try:
                    main_window = desktop.window(auto_id="frmMain")
                    if main_window.exists(timeout=1):
                        self.logger.info("Found main window via Desktop (frmMain)")
                        return main_window
                except:
                    pass
                    
            except Exception as e:
                self.logger.warning(f"Error using Desktop: {e}")
            
            # Method 2: Try with Application object
            if self.app:
                try:
                    # Try login window first
                    login_window = self.app.window(auto_id="frmTI_dangnhap")
                    if login_window.exists(timeout=1):
                        self.logger.info("Found login window via Application (frmTI_dangnhap)")
                        self.login_window = login_window
                        return login_window
                except:
                    pass
                
                try:
                    # Try main window
                    main_window = self.app.window(auto_id="frmMain")
                    if main_window.exists(timeout=1):
                        self.logger.info("Found main window via Application (frmMain)")
                        return main_window
                except:
                    pass
            
            # Method 3: Debug all windows
            self._debug_all_windows()
                
        except Exception as e:
            self.logger.warning(f"Error in fallback: {e}")
        
        return None


    def _focus_hps_window(self):
        """Focus on the HPS window to bring it to front"""
        try:
            if self.main_window:
                self.logger.info("Focusing on HPS window...")
                
                # Method 1: Use set_focus()
                try:
                    self.main_window.set_focus()
                    self.logger.info("✅ HPS window focused using set_focus()")
                    time.sleep(0.5)
                    return
                except Exception as e:
                    self.logger.warning(f"set_focus() failed: {e}")
                
                # Method 2: Use click_input() to focus
                try:
                    self.main_window.click_input()
                    self.logger.info("✅ HPS window focused using click_input()")
                    time.sleep(0.5)
                    return
                except Exception as e:
                    self.logger.warning(f"click_input() failed: {e}")
                
                # Method 3: Use move_mouse() and click
                try:
                    rect = self.main_window.rectangle()
                    center_x = (rect.left + rect.right) // 2
                    center_y = (rect.top + rect.bottom) // 2
                    self.main_window.move_mouse_input(coords=(center_x, center_y))
                    self.main_window.click_input(coords=(center_x, center_y))
                    self.logger.info("✅ HPS window focused using move_mouse + click")
                    time.sleep(0.5)
                    return
                except Exception as e:
                    self.logger.warning(f"move_mouse + click failed: {e}")
                
                # Method 4: Use Windows API to bring window to front
                try:
                    import win32gui
                    import win32con
                    
                    hwnd = self.main_window.handle
                    win32gui.SetForegroundWindow(hwnd)
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    self.logger.info("✅ HPS window focused using Windows API")
                    time.sleep(0.5)
                    return
                except Exception as e:
                    self.logger.warning(f"Windows API focus failed: {e}")
                
                self.logger.warning("All focus methods failed")
            else:
                self.logger.warning("No main window to focus")
                
        except Exception as e:
            self.logger.error(f"Error focusing HPS window: {e}")

    def focus_window(self):
        """Public method to focus HPS window"""
        self._focus_hps_window()
    
    def login(self) -> bool:
        """
        Perform login to HPS application
        
        Returns:
            bool: True if login successful, False otherwise
        """
        if not self.app or not self.main_window:
            self.logger.error("HPS application not started")
            return False
        
        try:
            username = self.config.get_username()
            password = self.config.get_password()
            
            if not username or not password:
                self.logger.error("Username or password not configured")
                return False
            
            # Check if already logged in (main window exists)
            if self.main_window and self.main_window.exists():
                try:
                    auto_id = self.main_window.element_info.automation_id
                    if auto_id == "frmMain":
                        self.logger.info("Already logged in - found main window (frmMain)")
                        # Press ESC to dismiss any popups
                        self._press_escape_post_login(attempts=3, delay_sec=0.2)
                        return True
                except:
                    # Window might be closed, reset main_window
                    self.main_window = None
                    pass
            
            # If main_window is None or doesn't exist, try to reconnect
            if not self.main_window or not self.main_window.exists():
                self.logger.info("Main window not available, attempting to reconnect...")
                if not self.connect_to_hps_process():
                    self.logger.error("Could not reconnect to HPS process")
                    return False
            
            self.logger.info("Attempting to login to HPS")
            
            # Look for login form elements
            login_controls = self._find_login_controls()
            
            if not login_controls:
                self.logger.error("Could not find login form controls")
                return False
            
            # Focus on the window before filling forms
            self._focus_hps_window()
            
            # Fill username
            if 'username' in login_controls:
                self.logger.info("Filling username field")
                try:
                    # Focus on username field first
                    login_controls['username'].set_focus()
                    time.sleep(0.2)
                    # Clear existing text and set new text
                    login_controls['username'].set_text("")
                    time.sleep(0.2)
                    login_controls['username'].type_keys(username)
                    self.logger.info(f"Username filled: {username}")
                    time.sleep(0.5)
                except Exception as e:
                    self.logger.warning(f"Error filling username: {e}")
            
            # Fill password
            if 'password' in login_controls:
                self.logger.info("Filling password field")
                try:
                    # Focus on password field
                    login_controls['password'].set_focus()
                    time.sleep(0.2)
                    # Clear existing text and set new text
                    login_controls['password'].set_text("")
                    time.sleep(0.2)
                    login_controls['password'].type_keys(password)
                    self.logger.info("Password filled")
                    time.sleep(0.5)
                except Exception as e:
                    self.logger.warning(f"Error filling password: {e}")
            
            # Click login button
            if 'login_button' in login_controls:
                self.logger.info("Clicking login button")
                login_controls['login_button'].click()
                time.sleep(3)  # Chờ popup xuất hiện
                
                # Ngay lập tức nhấn ESC để tắt popup
                self.logger.info("Pressing ESC to dismiss popups...")
                self._press_escape_post_login(attempts=3, delay_sec=0.2)

            # Wait for login to complete
            if self._wait_for_main_screen(timeout=15):
                self.logger.info("Login successful")
                
                # Nhấn ESC thêm lần nữa để chắc chắn
                self._press_escape_post_login(attempts=2, delay_sec=0.3)
                
                return True
            else:
                self.logger.error("Login failed - could not reach main screen")
                return False

                
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
    
    def _find_login_controls(self) -> dict:
        """
        Find login form controls using various methods
        
        Returns:
            dict: Dictionary containing found controls
        """
        controls = {}
        
        # Use login window if available, otherwise use main window
        target_window = self.login_window if self.login_window else self.main_window
        
        if not target_window:
            self.logger.error("No target window available for finding login controls")
            return controls
        
        try:
            self.logger.info(f"Looking for login controls in window: {target_window.window_text()}")
            
            # Method 1: Look for controls by automation ID (from automation_ids.txt)
            automation_ids = {
                'username': ['txt_userid', 'txtUser', 'txtLogin', 'username', 'txt_mabn', 'txtTenDangNhap'],
                'password': ['txt_pass', 'txtPass', 'txtPwd', 'password', 'txtMatKhau'],
                'login_button': ['bt_dongy', 'btnLogin', 'btnOk', 'btn_ok', 'bt_mabn', 'btnDongY']
            }
            
            for control_type, ids in automation_ids.items():
                for aid in ids:
                    try:
                        control = target_window.child_window(auto_id=aid)
                        if control.exists():
                            controls[control_type] = control
                            self.logger.info(f"Found {control_type} by automation ID: {aid}")
                            break
                    except:
                        continue
            
            # Method 2: Look for controls by class name and position
            if not controls:
                self.logger.info("Trying to find controls by class name...")
                try:
                    all_controls = target_window.descendants()
                    edit_controls = [c for c in all_controls if 'EDIT' in c.class_name().upper()]
                    button_controls = [c for c in all_controls if 'BUTTON' in c.class_name().upper()]
                    
                    self.logger.info(f"Found {len(edit_controls)} Edit controls and {len(button_controls)} Button controls")
                    
                    # Dựa vào test, có 3 Edit controls:
                    # Edit 0: Có thể là trường "Bác sĩ" 
                    # Edit 1: Có thể là trường "Mật khẩu" (bị đảo vị trí)
                    # Edit 2: Có thể là trường "Tên đăng nhập" (bị đảo vị trí)
                    
                    # Try to identify username field (Edit 2 - thứ 3)
                    if len(edit_controls) > 2 and 'username' not in controls:
                        controls['username'] = edit_controls[2]  # Edit 2
                        self.logger.info("Using Edit 2 as username field")
                    
                    # Try to identify password field (Edit 1 - thứ 2)
                    if len(edit_controls) > 1 and 'password' not in controls:
                        controls['password'] = edit_controls[1]  # Edit 1
                        self.logger.info("Using Edit 1 as password field")
                    
                    # Look for login button
                    if button_controls and 'login_button' not in controls:
                        for button in button_controls:
                            try:
                                button_text = button.window_text().lower()
                                if any(keyword in button_text for keyword in ['đồng ý', 'đăng nhập', 'login', 'ok', 'xác nhận']):
                                    controls['login_button'] = button
                                    self.logger.info(f"Found login button by text: {button_text}")
                                    break
                            except:
                                continue
                        
                        # If no button found by text, use last button (usually "Đồng ý")
                        if 'login_button' not in controls and button_controls:
                            controls['login_button'] = button_controls[-1]  # Button cuối cùng
                            self.logger.info("Using last Button control as login button")
                
                except Exception as e:
                    self.logger.warning(f"Error in class name method: {e}")
            
            # Method 3: Debug all controls if not found
            if not controls:
                self.logger.warning("No controls found by automation ID or class name")
                self._debug_all_controls(target_window)
            else:
                # Debug found controls
                self.logger.info("Found controls details:")
                for control_type, control in controls.items():
                    try:
                        auto_id = getattr(control, 'auto_id', lambda: 'N/A')()
                        class_name = control.class_name()
                        text = control.window_text()[:50]
                        self.logger.info(f"  {control_type}: {class_name} - '{text}' (auto_id: {auto_id})")
                    except:
                        self.logger.info(f"  {control_type}: <unable to get info>")
            
        except Exception as e:
            self.logger.warning(f"Error finding login controls: {e}")
        
        self.logger.info(f"Found controls: {list(controls.keys())}")
        return controls
    
    
    def _wait_for_main_screen(self, timeout: int = 20) -> bool:
        """
        Wait for main screen to appear after login.
        - Look for window with title containing "HPS" AND AutomationId = "frmMain"
        - Handle transient dialogs by pressing ESC
        """
        start_time = time.time()
        self.logger.info("Waiting for main screen to appear...")
        
        while time.time() - start_time < timeout:
            try:
                # First, try to dismiss any popups
                self._press_escape_post_login(attempts=1, delay_sec=0.1)
                
                # Look for main window with specific criteria
                main_window_found = self._find_main_window_by_criteria()
                
                if main_window_found:
                    # Final popup dismissal
                    self._press_escape_post_login(attempts=2, delay_sec=0.2)
                    self.logger.info("✅ Main screen found and popups dismissed")
                    return True

            except Exception as e:
                self.logger.debug(f"Waiting for main screen, transient error: {e}")

            time.sleep(0.5)

        # Final attempt
        try:
            self.logger.info("Final attempt to find main window...")
            main_window_found = self._find_main_window_by_criteria()
            if main_window_found:
                self._press_escape_post_login(attempts=2, delay_sec=0.2)
                self.logger.info("✅ Main screen found and popups dismissed (final attempt)")
                return True
        except Exception as e:
            self.logger.debug(f"Final attempt error: {e}")

        self.logger.warning("Could not find main window after timeout")
        return False
    
    def _find_main_window_by_criteria(self) -> bool:
        """
        Find main window using specific criteria:
        - Title contains "HPS" 
        - AutomationId = "frmMain"
        """
        try:
            # Method 1: Use findwindows for faster search
            try:
                from pywinauto import findwindows
                hps_windows = findwindows.find_windows(title_re=".*HPS.*")
                
                if hps_windows:
                    self.logger.info(f"Found {len(hps_windows)} HPS windows, checking for frmMain...")
                    
                    # Debug: List all HPS windows first
                    for i, window_handle in enumerate(hps_windows):
                        try:
                            app_temp = Application().connect(handle=window_handle)
                            window_temp = app_temp.window(handle=window_handle)
                            title = window_temp.window_text()
                            auto_id = window_temp.element_info.automation_id
                            self.logger.info(f"  Window {i}: '{title}' (AutoId: {auto_id})")
                        except Exception as e:
                            self.logger.info(f"  Window {i}: <error: {e}>")
                    
                    # Now check for frmMain
                    for window_handle in hps_windows:
                        try:
                            # Connect to the window
                            app_temp = Application().connect(handle=window_handle)
                            window_temp = app_temp.window(handle=window_handle)
                            
                            # Check criteria
                            title = window_temp.window_text()
                            auto_id = window_temp.element_info.automation_id
                            
                            if auto_id == "frmMain" and window_temp.is_enabled() and window_temp.is_visible():
                                self.main_window = window_temp
                                self.app = app_temp  # Update app reference
                                self.logger.info(f"✅ Found main window: '{title}' (AutoId: {auto_id})")
                                # Press ESC to dismiss any popups after finding main window
                                self._press_escape_post_login(attempts=3, delay_sec=0.2)
                                self.logger.info("✅ ESC pressed to dismiss popups")
                                return True
                            elif auto_id == "frmMain":
                                self.logger.debug(f"Found frmMain but not enabled/visible: '{title}'")
                            elif "HPS" in title and not "đăng nhập" in title.lower():
                                # Found HPS window but not frmMain - might be main window with different AutoId
                                self.logger.info(f"Found HPS window (not frmMain): '{title}' (AutoId: {auto_id})")
                                if window_temp.is_enabled() and window_temp.is_visible():
                                    self.main_window = window_temp
                                    self.app = app_temp  # Update app reference
                                    self.logger.info(f"✅ Using as main window: '{title}' (AutoId: {auto_id})")
                                    # Press ESC to dismiss any popups after finding main window
                                    self._press_escape_post_login(attempts=3, delay_sec=0.2)
                                    self.logger.info("✅ ESC pressed to dismiss popups")
                                    return True
                                
                        except Exception as e:
                            self.logger.debug(f"Error checking window {window_handle}: {e}")
                            continue
                            
            except Exception as e:
                self.logger.debug(f"findwindows method failed: {e}")
            
            # Method 2: Fallback to Desktop method
            self.logger.debug("Falling back to Desktop method...")
            windows = Desktop(backend="uia").windows()
            
            for win in windows:
                try:
                    title = win.window_text()
                    auto_id = win.element_info.automation_id
                    
                    # Check criteria: title contains "HPS" AND AutomationId = "frmMain"
                    if "HPS" in title and auto_id == "frmMain":
                        if win.is_enabled() and win.is_visible():
                            self.main_window = win
                            self.logger.info(f"✅ Found main window: '{title}' (AutoId: {auto_id})")
                            # Press ESC to dismiss any popups after finding main window
                            self._press_escape_post_login(attempts=3, delay_sec=0.2)
                            self.logger.info("✅ ESC pressed to dismiss popups")
                            return True
                        else:
                            self.logger.debug(f"Found frmMain but not enabled/visible: '{title}'")
                    elif "HPS" in title and not "đăng nhập" in title.lower():
                        # Found HPS window but not frmMain - might be main window with different AutoId
                        self.logger.info(f"Found HPS window (not frmMain): '{title}' (AutoId: {auto_id})")
                        if win.is_enabled() and win.is_visible():
                            self.main_window = win
                            self.logger.info(f"✅ Using as main window: '{title}' (AutoId: {auto_id})")
                            # Press ESC to dismiss any popups after finding main window
                            self._press_escape_post_login(attempts=3, delay_sec=0.2)
                            self.logger.info("✅ ESC pressed to dismiss popups")
                            return True
                            
                except Exception as e:
                    self.logger.debug(f"Error checking window: {e}")
                    continue
                    
            return False
            
        except Exception as e:
            self.logger.debug(f"Error finding main window by criteria: {e}")
            return False

    
    def is_logged_in(self) -> bool:
        """
        Check if currently logged in to HPS
        
        Returns:
            bool: True if logged in, False otherwise
        """
        try:
            if not self.app or not self.main_window:
                return False
            
            # Check if main window exists and is responsive
            return self.main_window.exists() and self.main_window.is_enabled()
        except:
            return False
    
    def close(self):
        """Close HPS application"""
        try:
            if self.app:
                self.app.kill()
                self.logger.info("HPS application closed")
        except Exception as e:
            self.logger.error(f"Error closing HPS application: {e}")

    def _find_thong_bao_dialog(self, timeout=5, retry=0.5):
        """Find 'Thông Báo' dialog reliably"""
        end_time = time.time() + timeout

        while time.time() < end_time:
            try:
                # 1. Scan toàn bộ windows trên Desktop
                for win in Desktop(backend="uia").windows():
                    try:
                        title = win.window_text()
                        ctrl_type = win.element_info.control_type

                        # Log tất cả cửa sổ liên quan đến "Thông"
                        if "Thông" in title:
                            self.logger.info(f"🔍 Candidate window: '{title}' | CtrlType={ctrl_type}")

                        if "Thông Báo" in title and ctrl_type == "Window":
                            self.logger.info(f"✅ Found 'Thông Báo' via Desktop(): {title}")
                            return win
                    except Exception:
                        continue

                # 2. Fallback: check top_window của app
                if self.app:
                    try:
                        top_win = self.app.top_window()
                        if "Thông Báo" in top_win.window_text():
                            self.logger.info("✅ Found 'Thông Báo' via app.top_window()")
                            return top_win
                    except Exception:
                        pass

            except Exception:
                pass

            time.sleep(retry)


        return None


    def _close_thong_bao_dialog(self, dialog) -> bool:
        """Try to close 'Thông Báo' dialog (click OK/Đồng ý or send ESC)"""
        try:
            if not dialog or not dialog.exists():
                return False

            dialog.set_focus()

            # 1. Tìm nút OK / Đồng ý
            for btn_text in ["OK", "Đồng ý"]:
                try:
                    btn = dialog.child_window(title=btn_text, control_type="Button")
                    if btn.exists():
                        btn.click_input()
                        self.logger.info(f"✅ Clicked '{btn_text}' button on 'Thông Báo'")
                        return True
                except Exception:
                    continue

            # 2. Tìm nút đóng (nút X)
            try:
                close_btn = dialog.child_window(control_type="Button", found_index=0)
                if close_btn.exists():
                    close_btn.click_input()
                    self.logger.info("✅ Clicked Close (X) button on 'Thông Báo'")
                    return True
            except Exception:
                pass

            # 3. Fallback: gửi ESC
            dialog.type_keys("{ESC}")
            self.logger.info("↪️ Sent ESC to 'Thông Báo' dialog")
            return True

        except Exception as e:
            self.logger.debug(f"Error closing 'Thông Báo' dialog: {e}")
            return False


    def _press_escape_post_login(self, attempts: int = 5, delay_sec: float = 1.0):
        """Close 'Thông Báo' dialog or send ESC to dismiss popups"""
        self.logger.info("🔍 Starting popup dismissal process...")

        for attempt in range(attempts):
            self.logger.info(f"Attempt {attempt + 1}/{attempts} - searching for 'Thông Báo'")

            dialog = self._find_thong_bao_dialog(timeout=2, retry=0.5)

            if dialog:
                self.logger.info(f"✅ Found 'Thông Báo' dialog: {dialog.window_text()}")
                if self._close_thong_bao_dialog(dialog):
                    self.logger.info("✅ Closed 'Thông Báo' dialog successfully")
                    return
                else:
                    self.logger.warning("❌ Failed to close 'Thông Báo' dialog")
            else:
                self.logger.info("ℹ️ No 'Thông Báo' found yet")

            # Nếu chưa tìm thấy → thử gửi ESC vào main window
            if self.main_window and self.main_window.exists():
                try:
                    self.main_window.set_focus()
                    self.main_window.type_keys("{ESC}")
                    self.logger.info("↪️ Sent ESC to main window")
                except Exception as e:
                    self.logger.debug(f"ESC send failed: {e}")

            time.sleep(delay_sec)

        self.logger.warning("⚠️ Could not dismiss 'Thông Báo' after all attempts")

