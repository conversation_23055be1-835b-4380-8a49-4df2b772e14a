#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra việc tìm và focus vào các trường nhập liệu trong cửa sổ đăng nhập HPS
"""

import sys
import os
import time
import logging
from pywinauto import Application, findwindows
from pywinauto.controls.uiawrapper import UIAWrapper

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_login_controls():
    """Test tìm và focus vào các trường nhập liệu"""
    try:
        logger.info("🔍 Tìm cửa sổ đăng nhập HPS...")
        
        # Tìm cửa sổ đăng nhập
        windows = findwindows.find_windows(title_re=".*Đăng nhập.*")
        
        if not windows:
            logger.error("Không tìm thấy cửa sổ đăng nhập")
            return False
        
        logger.info(f"Tìm thấy {len(windows)} cửa sổ đăng nhập")
        
        # Kết nối với cửa sổ đầu tiên
        window_handle = windows[0]
        app = Application(backend="uia").connect(handle=window_handle)
        window = app.window(handle=window_handle)
        
        logger.info(f"Kết nối với cửa sổ: {window.window_text()}")
        logger.info(f"AutomationId: {window.element_info.automation_id}")
        
        # Tìm tất cả controls
        logger.info("🔍 Tìm tất cả controls trong cửa sổ...")
        all_controls = window.descendants()
        logger.info(f"Tìm thấy {len(all_controls)} controls")
        
        # Tìm các trường nhập liệu
        edit_controls = []
        button_controls = []
        
        for i, control in enumerate(all_controls):
            try:
                class_name = control.class_name()
                auto_id = getattr(control, 'auto_id', lambda: 'N/A')()
                text = control.window_text()[:50]
                
                # Tìm Edit controls (bao gồm cả WindowsForms10.EDIT)
                if 'EDIT' in class_name.upper():
                    edit_controls.append((i, control, auto_id, text))
                # Tìm Button controls (bao gồm cả WindowsForms10.BUTTON)
                elif 'BUTTON' in class_name.upper():
                    button_controls.append((i, control, auto_id, text))
                
                logger.info(f"Control {i}: {class_name} - '{text}' (auto_id: {auto_id})")
                
            except Exception as e:
                logger.warning(f"Error getting control {i} info: {e}")
        
        logger.info(f"\n📝 Tìm thấy {len(edit_controls)} Edit controls:")
        for i, (idx, control, auto_id, text) in enumerate(edit_controls):
            logger.info(f"  Edit {i}: '{text}' (auto_id: {auto_id})")
            
            # Thử focus và nhập text
            try:
                logger.info(f"    Thử focus vào Edit {i}...")
                control.set_focus()
                time.sleep(0.5)
                
                # Thử nhập text test
                control.set_text("")
                control.type_keys("test_input")
                logger.info(f"    ✅ Đã nhập text vào Edit {i}")
                time.sleep(1)
                
                # Clear text
                control.set_text("")
                logger.info(f"    ✅ Đã clear text trong Edit {i}")
                
            except Exception as e:
                logger.warning(f"    ❌ Lỗi khi thao tác với Edit {i}: {e}")
        
        logger.info(f"\n🔘 Tìm thấy {len(button_controls)} Button controls:")
        for i, (idx, control, auto_id, text) in enumerate(button_controls):
            logger.info(f"  Button {i}: '{text}' (auto_id: {auto_id})")
        
        return True
        
    except Exception as e:
        logger.error(f"Lỗi: {e}")
        return False

if __name__ == "__main__":
    test_login_controls()
