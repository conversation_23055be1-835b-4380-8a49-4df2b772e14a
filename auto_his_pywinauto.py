#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script automation cho ứng dụng HPS sử dụng pywinauto (Element-based automation)
Tác giả: Auto-generated script
Mô tả: Tự động hóa quá trình tìm kiếm và xử lý dữ liệu trong HPS không chiếm chuột
"""

import time
import sys
import logging
from pywinauto import Application, findwindows
from pywinauto.controls.uiawrapper import UIAWrapper
# from pywinauto.controls.win32_controls import Win32Window
from pywinauto import timings

# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Cấu hình timings để tăng độ ổn định
timings.Timings.window_find_timeout = 10
timings.Timings.app_start_timeout = 10

class HPSElementAutomation:
    def __init__(self):
        """
        Khởi tạo class HPSElementAutomation với thông tin từ inspector
        """
        self.app_title_pattern = "HPS [V.03.09.2025]"  # Pattern để tìm ứng dụng
        self.app_process_name = None  # Sẽ được xác định động
        self.search_code = "15007567"  # Mã cần nhập
        self.app = None  # Đối tượng Application của pywinauto
        self.main_window = None  # Cửa sổ chính của ứng dụng
        self.grid_control = None  # Control của DataGridView để sử dụng cho right-click
        
        # AutomationId từ inspector
        self.main_window_id = "frmMain"
        self.search_box_id = "txt_TimKiem"
        self.grid_id = "grid_HoSo"
        
    def find_and_connect_hps_app(self):
        """
        Tìm và kết nối với ứng dụng HPS
        Returns: True nếu thành công, False nếu thất bại
        """
        try:
            logger.info("🔍 Đang tìm ứng dụng HPS...")
            
            # Tìm tất cả cửa sổ có chứa "HPS" trong title
            windows = findwindows.find_windows(title_re=".*HPS.*")
            
            if not windows:
                logger.error("❌ Không tìm thấy cửa sổ ứng dụng HPS nào")
                return False
            
            # Tìm cửa sổ phù hợp nhất
            target_window = None
            for window_handle in windows:
                try:
                    # Thử kết nối với cửa sổ
                    app_temp = Application().connect(handle=window_handle)
                    window_temp = app_temp.window(handle=window_handle)
                    
                    # Kiểm tra title có chứa pattern mong muốn
                    window_title = window_temp.window_text()
                    if "HPS" in window_title and "Bùi Xuân Bách" in window_title:
                        target_window = window_handle
                        self.app = app_temp
                        self.main_window = window_temp
                        logger.info(f"✅ Đã tìm thấy và kết nối với: {window_title}")
                        break
                        
                except Exception as e:
                    logger.debug(f"Không thể kết nối với window {window_handle}: {e}")
                    continue
            
            if not target_window:
                # Thử cách khác: kết nối theo process
                logger.info("🔄 Thử tìm theo danh sách process...")
                try:
                    # Tìm process có tên chứa "HPS" hoặc các tên thường gặp
                    possible_names = ["HPS", "hps", "Hospital", "BHXH", "BHYT"]
                    
                    for process_name in possible_names:
                        try:
                            self.app = Application().connect(path=f"{process_name}.exe")
                            self.main_window = self.app.window(auto_id=self.main_window_id)
                            logger.info(f"✅ Đã kết nối với process: {process_name}.exe")
                            break
                        except:
                            continue
                            
                except Exception as e:
                    logger.debug(f"Không thể kết nối theo process: {e}")
            
            if self.app and self.main_window:
                # Đảm bảo cửa sổ hiển thị - KHÔNG set_focus() toàn cửa sổ để tránh chiếm chuột
                try:
                    # Chỉ restore nếu bị minimize, không set_focus() toàn cửa sổ
                    if self.main_window.is_minimized():
                        self.main_window.restore()
                        logger.info("✅ Đã restore cửa sổ HPS")
                    
                    # Kiểm tra kết nối thành công
                    title = self.main_window.window_text()
                    logger.info(f"✅ Đã kết nối với ứng dụng HPS: {title[:50]}...")
                    logger.info("🚀 Sẽ chỉ click vào controls cần thiết - không focus toàn cửa sổ!")
                    return True
                    
                except Exception as e:
                    logger.warning(f"⚠️ Không thể kiểm tra cửa sổ: {e}")
                    return True  # Vẫn tiếp tục nếu kết nối được
            else:
                logger.error("❌ Không thể kết nối với ứng dụng HPS")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi khi tìm ứng dụng: {e}")
            return False
    
    def input_search_code(self):
        """
        Tìm ô tìm kiếm và nhập mã bằng AutomationId
        Returns: True nếu thành công, False nếu thất bại
        """
        try:
            logger.info("📝 Đang tìm ô tìm kiếm...")
            
            # Tìm ô tìm kiếm bằng AutomationId - sử dụng descendants để tìm trong toàn bộ cây
            search_box = None
            
            # Thử nhiều cách tìm kiếm
            try:
                # Cách 1: Tìm trực tiếp bằng auto_id (không chỉ định control_type)
                search_box = self.main_window.descendants(auto_id=self.search_box_id)[0]
                logger.info(f"✅ Tìm thấy ô tìm kiếm bằng descendants: {self.search_box_id}")
            except:
                try:
                    # Cách 2: Tìm bằng control_type TextBox
                    all_textboxes = self.main_window.descendants(control_type="TextBox")
                    for textbox in all_textboxes:
                        if textbox.automation_id() == self.search_box_id:
                            search_box = textbox
                            logger.info(f"✅ Tìm thấy ô tìm kiếm bằng TextBox: {self.search_box_id}")
                            break
                except:
                    logger.warning("⚠️ Không tìm thấy bằng TextBox, thử cách khác...")
            
            if search_box:
                logger.info(f"✅ Đã tìm thấy ô tìm kiếm: {self.search_box_id}")
                try:
                    logger.info(f"   📍 Control Type: {search_box.element_info.control_type}")
                    logger.info(f"   📍 Class Name: {search_box.class_name()}")
                except:
                    logger.info(f"   📍 Object Type: {type(search_box)}")
                    logger.info(f"   📍 Object: {search_box}")
                
                # Click vào ô tìm kiếm để có con trỏ, sau đó nhập mã
                try:
                    # BƯỚC 1: Click vào ô tìm kiếm để focus (cần thiết cho HwndWrapper)
                    logger.info("🎯 Click vào ô tìm kiếm để focus...")
                    search_box.click_input()  # Click để có con trỏ
                    time.sleep(0.3)  # Đợi focus
                    
                    # BƯỚC 2: Xóa nội dung cũ và nhập mã mới
                    logger.info(f"⌨️ Đang nhập mã: {self.search_code}")
                    search_box.type_keys("^a")  # Ctrl+A để select all
                    time.sleep(0.1)
                    search_box.type_keys(self.search_code)  # Nhập mã
                    time.sleep(0.3)
                    search_box.type_keys("{ENTER}")  # Enter để tìm kiếm
                    time.sleep(2)  # Đợi kết quả tìm kiếm
                    
                    logger.info(f"✅ Đã nhập mã thành công: {self.search_code}")
                    return True
                    
                except Exception as e:
                    logger.warning(f"⚠️ Phương pháp click + type_keys thất bại: {e}")
                    
                    # Fallback 1: Thử send_chars (có thể không hoạt động nhưng thử)
                    try:
                        logger.info("🔄 Fallback: Thử send_chars...")
                        search_box.click_input()  # Đảm bảo focus
                        time.sleep(0.2)
                        search_box.send_chars("^a")  # Ctrl+A
                        search_box.send_chars(self.search_code)  # Nhập mã
                        search_box.send_chars("{ENTER}")  # Enter
                        time.sleep(2)
                        logger.info(f"✅ Fallback send_chars thành công: {self.search_code}")
                        return True
                    except Exception as e2:
                        logger.warning(f"⚠️ send_chars fallback thất bại: {e2}")
                    
                    # Fallback 2: Thử set_text nếu có
                    try:
                        if hasattr(search_box, 'set_text'):
                            logger.info("🔄 Fallback: Thử set_text...")
                            search_box.set_text("")  # Xóa
                            search_box.set_text(self.search_code)  # Nhập
                            search_box.type_keys("{ENTER}")  # Enter
                            time.sleep(2)
                            logger.info(f"✅ Fallback set_text thành công: {self.search_code}")
                            return True
                        else:
                            logger.info("ℹ️ Không có method set_text")
                    except Exception as e3:
                        logger.warning(f"⚠️ set_text fallback thất bại: {e3}")
                    
                    logger.error(f"❌ Tất cả phương pháp nhập liệu đều thất bại")
                    return False
                        
            else:
                logger.error(f"❌ Không tìm thấy ô tìm kiếm với AutomationId: {self.search_box_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi khi nhập mã tìm kiếm: {e}")
            return False
    
    def select_grid_data(self):
        """
        Tương tác với DataGridView bằng AutomationId
        Returns: True nếu thành công, False nếu thất bại
        """
        try:
            logger.info("🎯 Đang tìm DataGridView...")
            
            # Tìm DataGridView bằng AutomationId - sử dụng descendants
            grid = None
            
            try:
                # Cách 1: Tìm trực tiếp bằng auto_id
                grid = self.main_window.descendants(auto_id=self.grid_id)[0]
                logger.info(f"✅ Tìm thấy DataGridView bằng descendants: {self.grid_id}")
            except:
                try:
                    # Cách 2: Tìm bằng control_type DataGridView
                    all_grids = self.main_window.descendants(control_type="DataGridView")
                    for grid_item in all_grids:
                        if grid_item.automation_id() == self.grid_id:
                            grid = grid_item
                            logger.info(f"✅ Tìm thấy DataGridView bằng control_type: {self.grid_id}")
                            break
                except:
                    logger.warning("⚠️ Không tìm thấy bằng DataGridView, thử cách khác...")
            
            if grid:
                logger.info(f"✅ Đã tìm thấy DataGridView: {self.grid_id}")
                try:
                    logger.info(f"   📍 Control Type: {grid.element_info.control_type}")
                    logger.info(f"   📍 Class Name: {grid.class_name()}")
                except:
                    logger.info(f"   📍 Object Type: {type(grid)}")
                    logger.info(f"   📍 Object: {grid}")
                
                # Lưu grid để sử dụng cho right-click
                self.grid_control = grid
                
                # Click vào grid để focus, sau đó chọn dòng đầu tiên
                try:
                    # Kiểm tra grid có data không
                    rows = grid.descendants(control_type="Row")
                    if rows:
                        logger.info(f"✅ DataGridView có {len(rows)} dòng dữ liệu")
                        
                        # Click vào grid để focus trước khi điều hướng
                        logger.info("🎯 Click vào DataGridView để focus...")
                        grid.click_input()
                        time.sleep(0.3)
                        
                        # Chọn dòng đầu tiên
                        grid.type_keys("{HOME}")  # Di chuyển về đầu
                        time.sleep(0.2)
                        grid.type_keys("{DOWN}")  # Chọn dòng đầu tiên
                        time.sleep(0.2)
                        logger.info("✅ Đã chọn dòng đầu tiên trong grid")
                    else:
                        logger.info("ℹ️ DataGridView không có dữ liệu hoặc chưa load")
                        # Vẫn click để focus ngay cả khi không có data
                        logger.info("🎯 Click vào DataGridView để chuẩn bị...")
                        grid.click_input()
                        time.sleep(0.3)
                        
                except Exception as e:
                    logger.warning(f"⚠️ Không thể chọn row, nhưng đã tìm thấy grid: {e}")
                    # Fallback: chỉ click để focus
                    try:
                        grid.click_input()
                        logger.info("✅ Đã click vào grid (fallback)")
                    except:
                        pass
                
                return True
            else:
                logger.error(f"❌ Không tìm thấy DataGridView với AutomationId: {self.grid_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi khi tương tác với grid: {e}")
            return False
    
    def perform_right_click_action(self):
        """
        Thực hiện right-click trên element được chọn và xử lý menu context
        Returns: True nếu thành công, False nếu thất bại
        """
        try:
            logger.info("🖱️ Đang thực hiện right-click...")
            
            # Sử dụng grid_control đã lưu từ bước trước
            if self.grid_control:
                logger.info("✅ Sử dụng grid control đã lưu")
                
                # Right-click trên grid KHÔNG chiếm chuột
                try:
                    # Sử dụng right_click_input với coordinates relative
                    self.grid_control.right_click_input()
                    time.sleep(1.5)  # Đợi menu context hiện ra
                    
                    logger.info("✅ Đã thực hiện right-click trên DataGridView")
                    
                    # Tìm menu context và chọn "Gửi lại"
                    return self.find_and_click_send_menu()
                    
                except Exception as e:
                    logger.warning(f"⚠️ right_click_input thất bại, thử cách khác: {e}")
                    
                    # Fallback: sử dụng phím tắt
                    try:
                        # Sử dụng phím Menu (Application key) hoặc Shift+F10
                        self.grid_control.send_chars("{APPS}")  # Phím Menu
                        time.sleep(1)
                        logger.info("✅ Đã mở menu context bằng phím APPS")
                        return self.find_and_click_send_menu()
                        
                    except Exception as e2:
                        logger.warning(f"⚠️ Phím APPS thất bại, thử Shift+F10: {e2}")
                        try:
                            self.grid_control.send_chars("+{F10}")  # Shift+F10
                            time.sleep(1)
                            logger.info("✅ Đã mở menu context bằng Shift+F10")
                            return self.find_and_click_send_menu()
                        except Exception as e3:
                            logger.error(f"❌ Tất cả cách mở menu đều thất bại: {e3}")
                            return False
            else:
                logger.error("❌ Không có grid control để right-click")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi khi right-click: {e}")
            return False
    
    def find_and_click_send_menu(self):
        """
        Tìm và click vào menu item "Gửi lại"
        Returns: True nếu thành công, False nếu thất bại
        """
        try:
            logger.info("🔍 Đang tìm menu 'Gửi lại'...")
            
            # Thử tìm menu context bằng nhiều cách - sử dụng descendants để tìm toàn bộ
            possible_menu_texts = ["Gửi lại", "Send", "Resend", "Gởi lại", "Gui lai"]
            
            # Cách 1: Tìm trong tất cả descendants
            try:
                all_menu_items = self.main_window.descendants(control_type="MenuItem")
                logger.info(f"📋 Tìm thấy {len(all_menu_items)} menu items trong toàn bộ ứng dụng")
                
                for menu_item in all_menu_items:
                    try:
                        menu_text = menu_item.window_text()
                        if any(text in menu_text for text in possible_menu_texts):
                            logger.info(f"✅ Tìm thấy menu: '{menu_text}'")
                            menu_item.click_input()
                            time.sleep(0.5)
                            return True
                    except:
                        continue
                        
            except Exception as e:
                logger.debug(f"Lỗi khi tìm menu items: {e}")
            
            # Cách 2: Tìm popup/context menu windows
            try:
                logger.info("🔄 Thử tìm popup menu...")
                
                # Tìm tất cả popup/context menu
                popup_windows = self.main_window.descendants(control_type="PopupItem")
                for popup in popup_windows:
                    try:
                        popup_text = popup.window_text()
                        if any(text in popup_text for text in possible_menu_texts):
                            logger.info(f"✅ Tìm thấy popup menu: '{popup_text}'")
                            popup.click_input()
                            time.sleep(0.5)
                            return True
                    except:
                        continue
                        
            except Exception as e:
                logger.debug(f"Lỗi khi tìm popup menu: {e}")
            
            # Cách 3: Sử dụng phím tắt thông minh
            logger.info("⌨️ Thử sử dụng phím tắt...")
            
            # Danh sách phím tắt có thể
            key_combinations = [
                "s",           # Phím S cho "Send"/"Gửi"
                "g",           # Phím G cho "Gửi"
                "{ENTER}",     # Enter để chọn item được highlight
                "{DOWN}s",     # Down arrow rồi S
                "{DOWN}{ENTER}", # Down arrow rồi Enter
            ]
            
            for key_combo in key_combinations:
                try:
                    if self.grid_control:
                        self.grid_control.send_chars(key_combo)
                    else:
                        self.main_window.send_chars(key_combo)
                    time.sleep(0.5)
                    logger.info(f"✅ Đã thử phím tắt: {key_combo}")
                    return True  # Giả định thành công, vì khó kiểm tra
                    
                except Exception as e:
                    logger.debug(f"Phím tắt {key_combo} thất bại: {e}")
                    continue
            
            # Cách 4: Fallback - thử click vào vị trí ước tính của menu
            logger.info("🎯 Thử click vào vị trí ước tính của menu...")
            try:
                if self.grid_control:
                    # Click vào vị trí tương đối trong grid (giả định menu xuất hiện gần đó)
                    rect = self.grid_control.rectangle()
                    # Click vào góc dưới bên phải của grid (vị trí thường có menu)
                    estimated_x = rect.right - 100
                    estimated_y = rect.bottom - 50
                    
                    # Sử dụng click_input với tọa độ tương đối
                    self.grid_control.click_input(coords=(estimated_x - rect.left, estimated_y - rect.top))
                    time.sleep(0.5)
                    logger.info(f"✅ Đã thử click vào vị trí ước tính: ({estimated_x}, {estimated_y})")
                    return True
                    
            except Exception as e:
                logger.debug(f"Click vị trí ước tính thất bại: {e}")
            
            logger.warning("⚠️ Không tìm thấy menu 'Gửi lại' bằng bất kỳ cách nào")
            logger.info("💡 Có thể menu đã được kích hoạt, hãy kiểm tra ứng dụng")
            return True  # Return True vì có thể đã thành công nhưng không detect được
            
        except Exception as e:
            logger.error(f"❌ Lỗi khi tìm menu: {e}")
            return False
    
    def run_element_automation(self):
        """
        Chạy toàn bộ quy trình automation dựa trên element
        Returns: True nếu thành công, False nếu thất bại
        """
        logger.info("🚀 Bắt đầu quy trình Element-based Automation cho HPS...")
        logger.info("=" * 60)
        
        try:
            # Bước 1: Kết nối với ứng dụng HPS
            logger.info("\n📋 BƯỚC 1: Kết nối với ứng dụng HPS")
            if not self.find_and_connect_hps_app():
                logger.error("❌ Không thể kết nối với ứng dụng HPS")
                return False
            
            # Bước 2: Nhập mã tìm kiếm
            logger.info("\n📋 BƯỚC 2: Nhập mã tìm kiếm")
            if not self.input_search_code():
                logger.error("❌ Không thể nhập mã tìm kiếm")
                return False
            
            # Bước 3: Tương tác với DataGridView
            logger.info("\n📋 BƯỚC 3: Chọn dữ liệu trong DataGridView")
            if not self.select_grid_data():
                logger.error("❌ Không thể tương tác với DataGridView")
                return False
            
            # Bước 4: Right-click và chọn menu
            logger.info("\n📋 BƯỚC 4: Right-click và chọn 'Gửi lại'")
            if not self.perform_right_click_action():
                logger.error("❌ Không thể thực hiện right-click hoặc chọn menu")
                return False
            
            # Bước 5: Hoàn thành
            logger.info("\n📋 BƯỚC 5: Hoàn thành quy trình")
            
            logger.info("\n" + "=" * 60)
            logger.info("🎉 HOÀN THÀNH! Element-based automation đã thực hiện thành công!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Lỗi trong quy trình automation: {e}")
            return False
    
    def get_app_info(self):
        """
        Lấy thông tin chi tiết về ứng dụng (debugging)
        """
        if self.main_window:
            try:
                logger.info("📊 THÔNG TIN ỨNG DỤNG:")
                logger.info(f"  📝 Title: {self.main_window.window_text()}")
                logger.info(f"  🆔 Class: {self.main_window.class_name()}")
                logger.info(f"  🔧 Handle: {self.main_window.handle}")
                
                # Liệt kê các controls con
                logger.info("📋 CÁC CONTROLS CHÍNH:")
                controls = self.main_window.children()
                for i, control in enumerate(controls[:10]):  # Chỉ hiển thị 10 controls đầu
                    try:
                        auto_id = control.automation_id() if hasattr(control, 'automation_id') else "N/A"
                        logger.info(f"  {i+1}. {control.class_name()} - AutoID: {auto_id}")
                    except:
                        logger.info(f"  {i+1}. {control.class_name()} - AutoID: N/A")
                        
            except Exception as e:
                logger.error(f"❌ Lỗi khi lấy thông tin app: {e}")

def main():
    """
    Hàm main để chạy element-based automation
    """
    try:
        logger.info("🤖 HPS Element-based Automation Script")
        logger.info("📌 Script này không chiếm chuột và tương tác trực tiếp với elements")
        logger.info("🛑 Nhấn Ctrl+C để dừng script")
        logger.info("")
        
        # Tạo đối tượng automation
        automation = HPSElementAutomation()
        
        # Đếm ngược trước khi bắt đầu
        for i in range(3, 0, -1):
            logger.info(f"⏰ Bắt đầu sau {i} giây...")
            time.sleep(1)
        
        # Chạy automation
        success = automation.run_element_automation()
        
        if success:
            logger.info("\n✅ Element-based automation hoàn thành thành công!")
            
            # Hiển thị thông tin debug nếu cần
            logger.info("\n🔧 Thông tin debug:")
            automation.get_app_info()
        else:
            logger.error("\n❌ Element-based automation gặp lỗi!")
            
        logger.info("\n👋 Cảm ơn bạn đã sử dụng script!")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Script đã bị dừng bởi người dùng")
    except Exception as e:
        logger.error(f"\n❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
