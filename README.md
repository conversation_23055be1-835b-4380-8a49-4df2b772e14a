HPS-A-BHYT — Tự động hóa nhập liệu HPS (Windows)

Tài liệu này hướng dẫn cài đặt, cấu hình và vận hành bộ công cụ tự động hóa HPS bằng Python (pywinauto) và xử lý Excel (openpyxl).

1. <PERSON><PERSON><PERSON><PERSON> thiệu

- <PERSON><PERSON><PERSON> tiêu: Tự động đăng nhập HPS.exe, mở chức năng E<PERSON>, tìm/nhập hồ sơ theo dữ liệu Excel, áp dụng quy tắc chọn "Mã đối tượng", ghi kết quả.
- Công nghệ chính: Python 3.10+, pywinauto, openpyxl, (tùy chọn) PyYAML, pyinstaller.

2. <PERSON><PERSON><PERSON> c<PERSON>u hệ thống

- Windows 10/11, quyền chạy ứng dụng HPS.exe.
- Python 3.10 trở lên (khuyến nghị 64-bit).
- <PERSON><PERSON> font/locale phù hợp nếu HPS hiển thị tiếng Việt.

3. <PERSON><PERSON><PERSON> bị môi trường

3.1. <PERSON><PERSON><PERSON> và thư viện

Kiểm tra phiên bản Python:

```bash
python --version
```

Cài thư viện cần thiết:

```bash
pip install pywinauto openpyxl pyyaml pyinstaller
```

3.2. Công cụ hỗ trợ UI Automation

- Cài/khởi chạy "Accessibility Insights for Windows" để lấy AutomationId, Name, ControlType của các control trong HPS.exe.

4. Dữ liệu đầu vào

- File Excel nguồn, ví dụ cột: MA_BN, MA_DOI_TUONG, NGAY_VAO, NGAY_RA, NOI_GIOI_THIEU.
- Đảm bảo định dạng ngày/tháng thống nhất (yyyy-mm-dd) hoặc đã được chuẩn hóa trong mã nguồn đọc Excel.

5. Cấu hình

- Dùng file `config.yaml` (khuyến nghị) để cấu hình đường dẫn và thông tin đăng nhập.

Ví dụ `config.yaml`:

```yaml
app:
  hps_path: "C:\Users\<USER>\Desktop\bin 2\Debug"
  start_timeout_sec: 30

auth:
  username: "qtm_tien"
  password: "12345"

io: (cho phép user browse)

rules:
  prefer_referred_code_for_ma_doi_tuong_3: true
```

6. Chạy chương trình

- Cách 1: chạy trực tiếp script chính (ví dụ `main.py`).

```bash
python main.py --config config.yaml
```

- Cách 2: chạy với tham số đường dẫn riêng lẻ (nếu được hỗ trợ):

```bash
python main.py --hps "C:\\...\\HPS.exe" --in D:\\data\\input.xlsx --out D:\\data\\output.xlsx
```

7. Cấu trúc mã nguồn (tham khảo)

- Module Excel
  - reader.py: Đọc file Excel → trả về danh sách bản ghi (dict/list[dict]).
  - writer.py: Ghi kết quả xử lý (Success/Error, thông báo) sang file mới.

- Module Automation
  - hps_login.py: Mở HPS.exe bằng Application().start(), điền user/pass, click Đăng nhập, chờ màn hình chính.
  - hps_emr.py: Điều hướng menu Tiện ích → Bệnh nhân → Hồ sơ bệnh án - EMR; tạo mới/tìm kiếm theo MA_BN và ngày; áp dụng quy tắc chọn "Mã đối tượng"; lưu; xử lý popup.

- Business Logic
  - Áp dụng Rule MA_DOI_TUONG (tham khảo mục 8).

- Logging
  - Sử dụng logging (file + console handler), ghi chi tiết từng bước và lỗi.

8. Quy tắc nghiệp vụ (Rule MA_DOI_TUONG) theo checklist

- Điều kiện theo combobox "ĐKKB chính" (`CBO_DKKBCHINH`):
  - Nếu giá trị = "Bệnh viện quân y B":
    - Nếu `MA_DOI_TUONG = 2` → chọn "2" trong `CBO_DOITUONG` rồi click `LBL_DOITUONG` để lưu; nếu có popup → nhấn `BTN_OK`.
  - Nếu giá trị khác "Bệnh viện quân y B":
    - Kiểm tra combobox "Nơi giới thiệu" (`CBO_NOIGIOITHIEU`):
      - Nếu có giá trị → chọn "1.3" trong `CBO_DOITUONG`.
      - Nếu không có giá trị → chọn "1.15" trong `CBO_DOITUONG`.
    - Click `LBL_DOITUONG` để lưu; nếu có popup → nhấn `BTN_OK`.

Ghi chú:
- Trước khi sửa đối tượng, cần xác định đúng đợt điều trị bằng cách so khớp `NGAY_VAO` từ Excel với giá trị hiển thị tại `DataGridView` (pattern hàng: "Đợt điều trị Row <n>").
- Quy tắc có thể cần tinh chỉnh theo thực tế màn hình HPS và danh mục tại đơn vị.

8.1. Bản đồ điều khiển (Automation IDs/Names)

- Menu
  - `HOSO_BENHAN_EMR=411`

- Nút / Text
  - `BTN_MOI=bt_mabn`
  - `TXT_MABN=txt_mabn`
  - `BTN_OK=btn_ok`

- Tab Items (dùng Name)
  - `TAB_SUA_DOITUONG` (Name="Sửa đối tượng")

- Combobox / Dropdown
  - `CBO_DOITUONG=cboMaDoiTuongKcb`
  - `CBO_DKKBCHINH=cb_dkkbcb`
  - `CBO_NOIGIOITHIEU=cbo_gioithieu`

- Label (trigger lưu)
  - `LBL_DOITUONG=label46`

- ListItem (dùng Name)
  - `ITEM_1_1` (Name="1.1"), `ITEM_1_2` (Name="1.2"), `ITEM_1_3` (Name="1.3"), `ITEM_1_4` (Name="1.4"), `ITEM_1_5` (Name="1.5"), `ITEM_1_15` (Name="1.15"), `ITEM_1_16` (Name="1.16"), `ITEM_2` (Name="2"), `ITEM_7` (Name="7"), `ITEM_9` (Name="9")

- DataGridView
  - `DATAGRIDVIEW=DataGridView`
  - `ROW_PREFIX="Đợt điều trị Row"`

9. Ghi log

- Mục tiêu: có thể truy vết toàn bộ quá trình xử lý: start app, login, từng MA_BN, thao tác UI, popup, kết quả.
- Khuyến nghị cấu hình 2 handler: console (INFO) và file (DEBUG). Một file log/phiên chạy để tiện đối chiếu.

10. Kiểm thử

- Đăng nhập: kiểm thử với user/pass hợp lệ và trường hợp sai thông tin.
- Excel: thử đọc 5 bản ghi để xác nhận mapping cột/định dạng ngày.
- Rule: kiểm thử các trường hợp MA_DOI_TUONG 1, 2, 3 (có/không Nơi giới thiệu).
- Không tìm thấy hồ sơ: xác minh thông báo/lỗi được ghi nhận đúng.
- Tải lớn: chạy 100+ record để kiểm tra độ ổn định và timing.

11. Đóng gói & triển khai (tùy chọn)

- Đóng gói thành .exe bằng pyinstaller:

```bash
pyinstaller --onefile main.py
```

- Phân phối kèm `config.yaml` mẫu và hướng dẫn sử dụng.

12. Khắc phục sự cố

- HPS.exe không mở/khởi động chậm: tăng `start_timeout_sec` hoặc kiểm tra đường dẫn `hps_path`.
- Không tìm thấy control (AutomationId/Name): cập nhật locator theo thực tế UI bằng Accessibility Insights.
- Lỗi font/Unicode khi điền tiếng Việt: xác minh encoding, thử gửi phím theo từng bước thay vì set_text trực tiếp.
- Excel lỗi định dạng ngày: chuẩn hóa format trước khi nhập, hoặc ép kiểu trong `reader.py`.
- Bị chặn bởi UAC/quyền: chạy cmd/PowerShell ở chế độ Administrator.

13. Câu hỏi thường gặp (FAQ)

- Có bắt buộc dùng `config.yaml` không?
  - Không bắt buộc nhưng khuyến nghị để dễ cấu hình.
- Có thể chạy nền/ẩn cửa sổ HPS không?
  - pywinauto thao tác UI thực, cần cửa sổ hiển thị và focus ổn định.
- Có thể tạm dừng/tiếp tục giữa chừng?
  - Nên triển khai lưu checkpoint (dựa trên dòng Excel đã xử lý) trong `writer.py`.

14. Tài liệu liên quan

- pywinauto: `https://pywinauto.readthedocs.io/`
- Accessibility Insights for Windows: `https://accessibilityinsights.io/`
- openpyxl: `https://openpyxl.readthedocs.io/`

15. Checklist nhanh (tham khảo)

- Cài Python 3.10+, pip install các thư viện cần.
- Chuẩn bị Excel đầu vào đúng cột/định dạng.
- Lấy AutomationId bằng Accessibility Insights.
- Cấu hình `config.yaml` (đường dẫn HPS, Excel, user/pass).
- Chạy thử 5 record, kiểm tra log và file output.
- Chạy toàn bộ, theo dõi popup/lỗi ngoại lệ.

Checklist Phát triển Tool Tự động (Python + Pywinauto + Openpyxl)
1. Chuẩn bị môi trường

 Kiểm tra đã cài Python 3.10+ hay chưa

 Cài thư viện cần thiết:

pip install pywinauto openpyxl


 Dùng Accessibility Insights for Windows để lấy AutomationId của các control trong HPS.exe.

 Chuẩn bị file Excel đầu vào (cột: MA_BN, MA_DOI_TUONG, Ngày vào, Ngày ra).



22. Các bước phát triển
Module Excel
 reader.py: Đọc file Excel → trả về danh sách record (dict).
 writer.py: Ghi kết quả xử lý (Success/Error) vào file mới.

Module Automation

 hps_login.py:

Mở HPS.exe bằng Application().start()

Điền user, pass
Click nút đăng nhập
 hps_emr.py:
 
Vào menu Tiện ích → Bệnh nhân → Hồ sơ bệnh án - EMR

Chọn Mới, nhập MA_BN

Tìm hồ sơ theo Ngày vào

Áp dụng rule chọn "Mã đối tượng"

Lưu, xử lý popup thông báo

Business Logic

 Rule áp dụng MA_DOI_TUONG:

=1 → chọn 1.1

=3 → chọn 1.15

=3 & có Nơi giới thiệu → chọn 1.3

=2 → chọn 2

Logging

 Dùng logging (file + console handler).

 Ghi lại từng bước: start app, login, xử lý từng MA_BN, lỗi nếu có.

4. Kiểm thử

 Test login với user hợp lệ.

 Test đọc 5 record từ Excel.

 Test các trường hợp MA_DOI_TUONG khác nhau.

 Test khi không tìm thấy hồ sơ bệnh án.

 Test 100+ record để kiểm tra ổn định.

5. Đóng gói & Triển khai

 Đóng gói script thành .exe bằng pyinstaller:

pyinstaller --onefile main.py


 Cho phép chỉnh sửa file config.yaml để thay đổi:

Đường dẫn HPS.exe

Đường dẫn Excel

User/Pass

 Hướng dẫn sử dụng (1 file PDF hoặc README.md).