#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script đơn giản để test login HPS
"""

import sys
import os
import time
import logging
from pywinauto import Application, findwindows

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simple_login():
    """Test login đơn giản"""
    try:
        config = Config()
        username = config.get_username()
        password = config.get_password()
        
        logger.info(f"Testing login with username: {username}")
        
        # Tìm cửa sổ đăng nhập
        logger.info("🔍 Tìm cửa sổ đăng nhập...")
        windows = findwindows.find_windows(title_re=".*<PERSON><PERSON>ng nhập.*")
        
        if not windows:
            logger.error("<PERSON><PERSON><PERSON><PERSON> tì<PERSON> thấy cửa sổ đăng nhập")
            return False
        
        # Kết nối với cửa sổ
        window_handle = windows[0]
        app = Application(backend="uia").connect(handle=window_handle)
        window = app.window(handle=window_handle)
        
        logger.info(f"Kết nối với cửa sổ: {window.window_text()}")
        
        # Tìm Edit controls
        all_controls = window.descendants()
        edit_controls = [c for c in all_controls if 'EDIT' in c.class_name().upper()]
        button_controls = [c for c in all_controls if 'BUTTON' in c.class_name().upper()]
        
        logger.info(f"Tìm thấy {len(edit_controls)} Edit controls và {len(button_controls)} Button controls")
        
        if len(edit_controls) < 3:
            logger.error("Không đủ Edit controls")
            return False
        
        # Focus và nhập username (Edit 2 - thứ 3)
        logger.info("📝 Nhập username...")
        username_field = edit_controls[2]  # Edit 2
        username_field.set_focus()
        time.sleep(0.5)
        username_field.set_text("")
        username_field.type_keys(username)
        logger.info(f"✅ Username: {username}")
        
        # Focus và nhập password (Edit 1 - thứ 2)
        logger.info("🔒 Nhập password...")
        password_field = edit_controls[1]  # Edit 1
        password_field.set_focus()
        time.sleep(0.5)
        password_field.set_text("")
        password_field.type_keys(password)
        logger.info("✅ Password filled")
        
        # Click nút Đồng ý
        logger.info("🔘 Clicking login button...")
        login_button = button_controls[-1]  # Button cuối cùng
        login_button.click()
        logger.info("✅ Login button clicked")
        
        # Chờ một chút
        time.sleep(3)
        
        logger.info("🎉 Login test completed!")
        return True
        
    except Exception as e:
        logger.error(f"Lỗi: {e}")
        return False

if __name__ == "__main__":
    test_simple_login()
