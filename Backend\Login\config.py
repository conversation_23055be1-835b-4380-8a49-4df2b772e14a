"""
Configuration management for HPS automation tool
"""
import yaml
import os
from typing import Dict, Any, Optional


class Config:
    """Configuration manager for HPS automation"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        if not os.path.exists(self.config_path):
            # Return default config if file doesn't exist
            return self._get_default_config()
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except Exception as e:
            print(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Return default configuration"""
        return {
            'app': {
                'hps_path': r"C:\Users\<USER>\Desktop\bin 2\Debug\HPS.exe",
                'start_timeout_sec': 30
            },
            'auth': {
                'username': 'qtm_tien',
                'password': '12345'
            },
            'io': {
                'input_excel': '',
                'output_excel': ''
            },
            'rules': {
                'prefer_referred_code_for_ma_doi_tuong_3': True
            },
            'logging': {
                'level': 'INFO',
                'file': 'hps_automation.log',
                'console': True
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_hps_path(self) -> str:
        """Get HPS executable path"""
        return self.get('app.hps_path', '')
    
    def get_username(self) -> str:
        """Get login username"""
        return self.get('auth.username', '')
    
    def get_password(self) -> str:
        """Get login password"""
        return self.get('auth.password', '')
    
    def get_timeout(self) -> int:
        """Get application start timeout"""
        return self.get('app.start_timeout_sec', 30)
    
    def get_input_excel(self) -> str:
        """Get input Excel file path"""
        return self.get('io.input_excel', '')
    
    def get_output_excel(self) -> str:
        """Get output Excel file path"""
        return self.get('io.output_excel', '')
    
    def set_input_excel(self, path: str):
        """Set input Excel file path"""
        if 'io' not in self._config:
            self._config['io'] = {}
        self._config['io']['input_excel'] = path
    
    def set_output_excel(self, path: str):
        """Set output Excel file path"""
        if 'io' not in self._config:
            self._config['io'] = {}
        self._config['io']['output_excel'] = path
