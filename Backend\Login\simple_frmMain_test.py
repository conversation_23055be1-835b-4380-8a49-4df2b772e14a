import time
from pywinauto import Desktop

print("Testing frmMain...")
time.sleep(2)

try:
    desktop = Desktop(backend="uia")
    main_window = desktop.window(auto_id="frmMain")
    if main_window.exists():
        print("✅ MAIN WINDOW FOUND!")
        print(f"Title: {main_window.window_text()}")
    else:
        print("❌ frmMain not found")
        
    login_window = desktop.window(auto_id="frmTI_dangnhap")
    if login_window.exists():
        print("✅ LOGIN WINDOW FOUND!")
        print(f"Title: {login_window.window_text()}")
    else:
        print("❌ frmTI_dangnhap not found")
        
except Exception as e:
    print(f"Error: {e}")

print("Done.")



