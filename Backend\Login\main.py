"""
Main entry point for HPS automation tool
"""
import argparse
import logging
import sys
import os
from pathlib import Path

# Add Backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from hps_login import HPSLogin


def setup_logging(config: Config):
    """Setup logging configuration"""
    log_level = config.get('logging.level', 'INFO')
    log_file = config.get('logging.file', 'hps_automation.log')
    console_logging = config.get('logging.console', True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[]
    )
    
    # File handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logging.getLogger().addHandler(file_handler)
    
    # Console handler
    if console_logging:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        logging.getLogger().addHandler(console_handler)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='HPS Automation Tool')
    parser.add_argument('--config', '-c', default='config.yaml', 
                       help='Path to configuration file')
    parser.add_argument('--hps-path', help='Path to HPS executable')
    parser.add_argument('--username', help='HPS username')
    parser.add_argument('--password', help='HPS password')
    parser.add_argument('--input-excel', help='Input Excel file path')
    parser.add_argument('--output-excel', help='Output Excel file path')
    parser.add_argument('--test-login', action='store_true', 
                       help='Test login functionality only')
    parser.add_argument('--test-focus', action='store_true', 
                       help='Test window focus functionality only')
    parser.add_argument('--scan-ids', action='store_true', 
                       help='Scan and display all HPS AutomationIds')
    parser.add_argument('--auto-login', action='store_true', 
                       help='Automatically start HPS, scan IDs, and perform login')
    
    args = parser.parse_args()
    
    # Load configuration
    config = Config(args.config)
    
    # Override config with command line arguments
    if args.hps_path:
        config._config['app']['hps_path'] = args.hps_path
    if args.username:
        config._config['auth']['username'] = args.username
    if args.password:
        config._config['auth']['password'] = args.password
    if args.input_excel:
        config.set_input_excel(args.input_excel)
    if args.output_excel:
        config.set_output_excel(args.output_excel)
    
    # Setup logging
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting HPS Automation Tool")
    
    try:
        # Initialize HPS login
        hps_login = HPSLogin(config)
        
        # Test login functionality
        if args.test_login:
            logger.info("Testing login functionality...")
            
            # Start HPS
            if not hps_login.start_hps():
                logger.error("Failed to start HPS application")
                # Debug thêm
                try:
                    hps_login._debug_all_windows()
                except Exception as e:
                    logger.warning(f"Could not debug windows: {e}")
                return 1

            
            # Attempt login
            if hps_login.login():
                logger.info("Login test successful!")
                
                # Keep application open for a few seconds to verify
                import time
                time.sleep(5)
                
                hps_login.close()
                return 0
            else:
                logger.error("Login test failed!")
                hps_login.close()
                return 1
        
        # Test focus functionality
        elif args.test_focus:
            logger.info("Testing focus functionality...")
            
            # Start HPS
            if not hps_login.start_hps():
                logger.error("Failed to start HPS application")
                return 1
            
            # Test focus
            hps_login.focus_window()
            logger.info("Focus test completed!")
            
            # Keep application open for a few seconds to verify
            import time
            time.sleep(3)
            
            hps_login.close()
            return 0
        
        # Scan AutomationIds
        elif args.scan_ids:
            logger.info("Scanning HPS AutomationIds...")
            
            # Try to connect to existing HPS process
            if hps_login.connect_to_hps_process():
                automation_ids = hps_login.scan_hps_automation_ids()
                if automation_ids:
                    logger.info(f"Found {len(automation_ids)} AutomationIds:")
                    for auto_id in sorted(automation_ids):
                        logger.info(f"  {auto_id}")
                    
                    # Save to file
                    with open("hps_automation_ids_scan.txt", "w", encoding="utf-8") as f:
                        for auto_id in sorted(automation_ids):
                            f.write(f"{auto_id}\n")
                    logger.info("Results saved to 'hps_automation_ids_scan.txt'")
                else:
                    logger.warning("No AutomationIds found")
            else:
                logger.error("Could not connect to HPS process. Make sure HPS is running.")
                return 1
            
            return 0
        
        # Auto login workflow
        elif args.auto_login:
            logger.info("🚀 Starting automatic HPS login workflow...")
            
            # Start HPS (will auto-scan IDs)
            if not hps_login.start_hps():
                logger.error("❌ Failed to start HPS application")
                return 1
            
            # Attempt login
            if hps_login.login():
                logger.info("✅ Automatic login successful!")
                
                # Keep application open for a few seconds to verify
                import time
                time.sleep(5)
                
                hps_login.close()
                return 0
            else:
                logger.error("❌ Automatic login failed!")
                hps_login.close()
                return 1
        
        # Default behavior: Auto login workflow
        else:
            logger.info("🚀 Starting automatic HPS login workflow (default)...")
            
            # Start HPS (will auto-scan IDs)
            if not hps_login.start_hps():
                logger.error("❌ Failed to start HPS application")
                return 1
            
            # Attempt login
            if hps_login.login():
                logger.info("✅ Automatic login successful!")
                
                # Keep application open for a few seconds to verify
                import time
                time.sleep(5)
                
                hps_login.close()
                return 0
            else:
                logger.error("❌ Automatic login failed!")
                hps_login.close()
                return 1
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
